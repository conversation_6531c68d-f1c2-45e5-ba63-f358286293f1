/**
 * Utilitaire de chiffrement pour les données sensibles
 * Conforme aux recommandations de la CNIL pour la protection des données
 */

// Utilisation de l'API Web Crypto pour le chiffrement, recommandée par la CNIL
// pour sa robustesse et sa standardisation

/**
 * Génère une clé de chiffrement AES-GCM à partir d'une passphrase
 * @param passphrase - Phrase secrète utilisée pour dériver la clé
 * @returns Clé de chiffrement
 */
async function generateKey(passphrase: string): Promise<CryptoKey> {
  // Convertir la passphrase en ArrayBuffer
  const encoder = new TextEncoder();
  const passphraseData = encoder.encode(passphrase);

  // Utiliser PBKDF2 pour dériver une clé à partir de la passphrase
  const salt = encoder.encode('WeMaTrackerSalt'); // Sel fixe pour la dérivation
  const keyMaterial = await window.crypto.subtle.importKey(
    'raw',
    passphraseData,
    { name: 'PBKDF2' },
    false,
    ['deriveKey']
  );

  // Dériver la clé AES-GCM
  return window.crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt,
      iterations: 100000, // Nombre élevé d'itérations pour renforcer la sécurité
      hash: 'SHA-256'
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 }, // AES-256-GCM, recommandé par la CNIL
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * Chiffre une chaîne de caractères
 * @param text - Texte à chiffrer
 * @param passphrase - Phrase secrète pour le chiffrement
 * @returns Texte chiffré encodé en base64
 * @throws Error si le chiffrement échoue
 */
export async function encryptText(text: string, passphrase: string): Promise<string> {
  // Si le texte est vide, retourner une chaîne vide
  if (!text) return '';

  try {
    const key = await generateKey(passphrase);
    const encoder = new TextEncoder();
    const data = encoder.encode(text);

    // Générer un vecteur d'initialisation aléatoire
    const iv = window.crypto.getRandomValues(new Uint8Array(12));

    // Chiffrer les données
    const encryptedData = await window.crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv
      },
      key,
      data
    );

    // Concaténer IV et données chiffrées
    const result = new Uint8Array(iv.length + encryptedData.byteLength);
    result.set(iv);
    result.set(new Uint8Array(encryptedData), iv.length);

    // Encoder en base64 pour stockage ou transmission
    return btoa(String.fromCharCode(...result));
  } catch (error) {
    console.error('Erreur critique lors du chiffrement:', error);
    // Ne jamais retourner le texte en clair en cas d'erreur de sécurité
    throw new Error(`Échec du chiffrement: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
}

/**
 * Déchiffre une chaîne de caractères
 * @param encryptedText - Texte chiffré encodé en base64
 * @param passphrase - Phrase secrète pour le déchiffrement
 * @returns Texte déchiffré
 * @throws Error si le déchiffrement échoue
 */
export async function decryptText(encryptedText: string, passphrase: string): Promise<string> {
  // Si le texte est vide, retourner une chaîne vide
  if (!encryptedText) return '';

  try {
    const key = await generateKey(passphrase);

    // Décoder le texte chiffré de base64
    const encryptedData = Uint8Array.from(atob(encryptedText), c => c.charCodeAt(0));

    // Extraire l'IV (12 premiers octets)
    const iv = encryptedData.slice(0, 12);
    const ciphertext = encryptedData.slice(12);

    // Déchiffrer les données
    const decryptedData = await window.crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv
      },
      key,
      ciphertext
    );

    // Convertir le résultat en chaîne de caractères
    const decoder = new TextDecoder();
    return decoder.decode(decryptedData);
  } catch (error) {
    console.error('Erreur critique lors du déchiffrement:', error);
    // En cas d'erreur de déchiffrement, lever une exception plutôt que de retourner un marqueur
    throw new Error(`Échec du déchiffrement: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
}

/**
 * Vérifie si une chaîne est probablement chiffrée
 * @param text - Texte à vérifier
 * @returns true si le texte semble être chiffré
 */
export function isEncrypted(text: string): boolean {
  // Si le texte est vide ou null, il n'est pas chiffré
  if (!text) return false;

  // Vérifier si le texte est encodé en base64 et a une longueur minimale
  // La longueur minimale est de 24 caractères (12 octets pour l'IV + au moins 12 octets de données)
  const base64Regex = /^[A-Za-z0-9+/=]+$/;
  return text.length > 24 && base64Regex.test(text);
}

/**
 * Déchiffre un texte de manière sécurisée avec gestion d'erreur
 * @param text - Texte potentiellement chiffré
 * @param passphrase - Phrase secrète pour le déchiffrement
 * @returns Texte déchiffré ou marqueur sécurisé si échec
 */
export async function safeDecryptText(text: string, passphrase: string): Promise<string> {
  if (!text) return '';

  // Si le texte n'est pas chiffré, le retourner tel quel
  if (!isEncrypted(text)) {
    return text;
  }

  try {
    return await decryptText(text, passphrase);
  } catch (error) {
    console.error('SÉCURITÉ: Échec du déchiffrement, données potentiellement corrompues:', error);
    // SÉCURITÉ: Ne jamais retourner de données potentiellement chiffrées
    // Retourner un marqueur sécurisé pour indiquer l'échec
    return '[DONNÉES_CHIFFRÉES_NON_LISIBLES]';
  }
}

/**
 * Obtient la clé de chiffrement de l'application
 * @returns Passphrase utilisée pour le chiffrement
 */
export function getEncryptionKey(): string {
  // Utiliser une clé dérivée de l'URL Supabase pour garantir la cohérence
  // Cette approche évite de stocker la clé en clair dans le code
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
  const appKey = 'WeMaTracker-RGPD-Protection-Key';
  return `${appKey}-${supabaseUrl.split('//')[1].split('.')[0]}`;
}
