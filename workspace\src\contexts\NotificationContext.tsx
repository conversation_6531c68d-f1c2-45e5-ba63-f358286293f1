/**
 * Contexte pour la gestion des notifications
 * Fournit des fonctions pour créer, lire et marquer les notifications comme lues
 */

import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { Notification } from '@/types';
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useEncryption } from "@/hooks/useEncryption";
import { useProfileContext } from "./ProfileContext";
import { handleError, ErrorCode, createAppError } from "@/services/errorService";
import { logCreate, logRead, logUpdate } from "@/utils/secure-logging";
import { sendDesktopNotification } from "@/utils/notifications";
import { updateTaskbarBadge } from "@/utils/taskbar";
import { useSupabaseSubscription, onInsert, onUpdate } from '@/hooks/useSupabaseSubscription';

interface NotificationContextProps {
  notifications: Notification[];
  loading: boolean;
  unreadCount: number;
  getNotifications: () => Promise<void>;
  markNotificationAsRead: (id: string) => Promise<void>;
  createNotification: (userId: string, stepId: string, message: string) => Promise<void>;
}

const NotificationContext = createContext<NotificationContextProps | undefined>(undefined);

/**
 * Provider pour le contexte des notifications
 */
export const NotificationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  const { currentProfile } = useProfileContext();
  const { encrypt, decrypt, batchDecrypt } = useEncryption();

  /**
   * Récupère les notifications pour l'utilisateur courant
   */
  const getNotifications = async () => {
    if (!currentProfile) {
      setNotifications([]);
      setUnreadCount(0);
      return;
    }

    try {
      setLoading(true);

      // Journaliser l'opération
      logRead('notifications', null, currentProfile.id, 'Récupération des notifications');

      // Récupérer les notifications
      const { data, error } = await supabase
        .from('notifications')
        .select('*, created_by(id, name)')
        .eq('user_id', currentProfile.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw createAppError(
          `Erreur lors de la récupération des notifications: ${error.message}`,
          ErrorCode.DB_QUERY_ERROR
        );
      }

      // Déchiffrer les messages des notifications
      const processedNotifications = await Promise.all(data.map(async notification => {
        let message = notification.message;

        // Déchiffrer le message si nécessaire
        if (message) {
          message = await decrypt(message);
        }

        return {
          ...notification,
          message
        };
      }));

      setNotifications(processedNotifications);

      // Compter les notifications non lues
      const unread = processedNotifications.filter(n => !n.read).length;
      setUnreadCount(unread);

      // Mettre à jour le badge de la barre des tâches
      updateTaskbarBadge(unread);

    } catch (error) {
      handleError(error, 'NotificationContext.getNotifications');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Marque une notification comme lue
   * @param id - ID de la notification
   */
  const markNotificationAsRead = async (id: string) => {
    if (!currentProfile) return;

    try {
      // Journaliser l'opération
      logUpdate('notifications', id, currentProfile.id, 'Marquer une notification comme lue');

      // Vérifier si la notification existe et appartient au profil courant
      const { data: notifData, error: notifError } = await supabase
        .from('notifications')
        .select('*')
        .eq('id', id)
        .eq('user_id', currentProfile.id)
        .single();

      if (notifError) {
        throw createAppError(
          `Erreur lors de la vérification de la notification: ${notifError.message}`,
          ErrorCode.DB_QUERY_ERROR
        );
      }

      if (!notifData) {
        throw createAppError(
          'La notification n\'existe pas ou n\'appartient pas au profil courant',
          ErrorCode.VALIDATION_ERROR
        );
      }

      // Marquer la notification comme lue
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', id)
        .eq('user_id', currentProfile.id);

      if (error) {
        throw createAppError(
          `Erreur lors du marquage de la notification comme lue: ${error.message}`,
          ErrorCode.DB_QUERY_ERROR
        );
      }

      // Mettre à jour l'état local
      setNotifications(prev =>
        prev.map(n => n.id === id ? { ...n, read: true } : n)
      );

      // Mettre à jour le compteur de notifications non lues
      setUnreadCount(prev => Math.max(0, prev - 1));

      // Mettre à jour le badge de la barre des tâches
      updateTaskbarBadge(unreadCount - 1);

    } catch (error) {
      handleError(error, 'NotificationContext.markNotificationAsRead');
    }
  };

  /**
   * Crée une nouvelle notification
   * @param userId - ID du destinataire
   * @param stepId - ID de l'étape concernée
   * @param message - Message de la notification
   */
  const createNotification = async (userId: string, stepId: string, message: string) => {
    if (!currentProfile) return;

    try {
      // Journaliser l'opération
      logCreate('notifications', null, currentProfile.id, 'Création d\'une notification');

      // Chiffrer le message
      const encryptedMessage = await encrypt(message);

      // Créer la notification
      // Vérifier si currentProfile existe et a un ID
      if (!currentProfile || !currentProfile.id) {
        throw createAppError(
          "Impossible de créer une notification sans profil actif",
          ErrorCode.VALIDATION_ERROR
        );
      }

      // Vérifier si l'ID du profil existe dans la table assignees
      const { data: assigneeExists, error: assigneeError } = await supabase
        .from('assignees')
        .select('id')
        .eq('id', currentProfile.id)
        .single();

      if (assigneeError || !assigneeExists) {
        console.error("Le profil actuel n'existe pas dans la table assignees, impossible de créer une notification");
        throw createAppError(
          "Le profil actuel n'existe pas dans la base de données",
          ErrorCode.VALIDATION_ERROR
        );
      }

      // Modifier la structure de la table notifications pour utiliser assignee_id au lieu de created_by
      // Cette modification est temporaire et devrait être remplacée par une migration de base de données
      try {
        const { error } = await supabase
          .from('notifications')
          .insert({
            user_id: userId,
            step_id: stepId,
            created_by: currentProfile.id, // Utiliser l'ID de l'assignee actuel
            message: encryptedMessage,
            read: false
          });

        if (error) {
          // Journaliser l'erreur pour le débogage
          console.error("Erreur lors de la création de la notification:", error);

          throw createAppError(
            `Erreur lors de la création de la notification: ${error.message}`,
            ErrorCode.DB_QUERY_ERROR
          );
        }
      } catch (insertError) {
        console.error("Erreur lors de l'insertion de la notification:", insertError);
        throw insertError;
      }



      // Si la notification est pour l'utilisateur courant, rafraîchir les notifications
      if (userId === currentProfile.id) {
        await getNotifications();
      }

    } catch (error) {
      handleError(error, 'NotificationContext.createNotification');
    }
  };

  // Effet pour charger les notifications lorsque le profil change
  useEffect(() => {
    if (currentProfile) {
      getNotifications();
    } else {
      setNotifications([]);
      setUnreadCount(0);
    }
  }, [currentProfile]);

  // Utiliser le hook de souscription pour les notifications
  useSupabaseSubscription(
    'notifications',
    [
      // S'abonner aux nouvelles notifications pour le profil courant
      onInsert('notifications', (payload) => {
        if (currentProfile && payload.new && payload.new.user_id === currentProfile.id) {
          getNotifications();

          // Envoyer une notification de bureau
          sendDesktopNotification(
            'Nouvelle notification',
            'Vous avez reçu une nouvelle notification',
            '/lovable-uploads/wema-logo.png'
          );
        }
      }),

      // S'abonner aux mises à jour de notifications pour le profil courant
      // CORRECTION: Ne pas recharger toutes les notifications lors d'une mise à jour
      // car cela écrase l'état local et fait réapparaître les notifications lues
      onUpdate('notifications', (payload) => {
        if (currentProfile && payload.new && payload.new.user_id === currentProfile.id) {
          // Mettre à jour uniquement la notification modifiée dans l'état local
          // au lieu de recharger toutes les notifications
          setNotifications(prev =>
            prev.map(n =>
              n.id === payload.new.id
                ? { ...n, read: payload.new.read, message: payload.new.message }
                : n
            )
          );

          // Recalculer le compteur de notifications non lues
          setNotifications(prev => {
            const newUnreadCount = prev.filter(n => !n.read).length;
            setUnreadCount(newUnreadCount);
            updateTaskbarBadge(newUnreadCount);
            return prev;
          });
        }
      })
    ]
  );

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        loading,
        unreadCount,
        getNotifications,
        markNotificationAsRead,
        createNotification
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

/**
 * Hook pour utiliser le contexte des notifications
 * @returns Contexte des notifications
 * @throws {Error} Si utilisé en dehors d'un NotificationProvider
 */
export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
};
