/**
 * Script de test pour vérifier si les souscriptions temps réel fonctionnent
 * Ce script teste la connectivité et les souscriptions sur toutes les tables
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Charger les variables d'environnement depuis le fichier .env
function loadEnv() {
  const envPath = path.join(__dirname, '..', '.env');
  if (!fs.existsSync(envPath)) {
    console.error('Fichier .env non trouvé');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      env[key.trim()] = value.trim().replace(/^["']|["']$/g, '');
    }
  });
  
  return env;
}

async function testRealtime() {
  try {
    console.log('🧪 Test des souscriptions temps réel Supabase...\n');

    const env = loadEnv();
    const supabaseUrl = env.VITE_SUPABASE_URL;
    const supabaseKey = env.VITE_SUPABASE_API_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Variables d\'environnement Supabase manquantes dans .env');
    }

    console.log('📡 Connexion à Supabase...');
    console.log(`   URL: ${supabaseUrl}`);
    console.log(`   Clé: ${supabaseKey.substring(0, 20)}...`);

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Test de connectivité de base
    console.log('\n🔌 Test de connectivité de base...');
    const { data: testData, error: testError } = await supabase
      .from('assignees')
      .select('count')
      .limit(1);

    if (testError) {
      throw new Error(`Erreur de connectivité: ${testError.message}`);
    }
    console.log('   ✅ Connectivité OK');

    // Test des souscriptions temps réel
    console.log('\n⚡ Test des souscriptions temps réel...');
    
    const tables = ['clients', 'steps', 'step_assignments', 'assignees', 'notifications', 'pings'];
    const results = {};

    for (const table of tables) {
      console.log(`\n   📋 Test de la table '${table}'...`);
      
      try {
        // Créer un canal de test
        const channel = supabase.channel(`test-${table}-${Date.now()}`)
          .on('postgres_changes', { 
            event: '*', 
            schema: 'public', 
            table: table 
          }, (payload) => {
            console.log(`      🔔 Changement détecté sur ${table}:`, payload);
          });

        // S'abonner et attendre la confirmation
        const subscriptionPromise = new Promise((resolve, reject) => {
          channel.subscribe((status) => {
            if (status === 'SUBSCRIBED') {
              console.log(`      ✅ Souscription active pour '${table}'`);
              results[table] = 'OK';
              resolve(true);
            } else if (status === 'CHANNEL_ERROR' || status === 'CLOSED') {
              console.log(`      ❌ Erreur de souscription pour '${table}': ${status}`);
              results[table] = 'ERREUR';
              reject(new Error(status));
            }
          });
        });

        // Attendre la souscription avec timeout
        await Promise.race([
          subscriptionPromise,
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Timeout')), 5000)
          )
        ]);

        // Nettoyer le canal
        await supabase.removeChannel(channel);

      } catch (error) {
        console.log(`      ❌ Erreur pour '${table}': ${error.message}`);
        results[table] = 'ERREUR';
      }
    }

    // Résumé des résultats
    console.log('\n📊 RÉSUMÉ DES TESTS :');
    console.log('=====================================');
    
    let allOk = true;
    for (const [table, status] of Object.entries(results)) {
      const icon = status === 'OK' ? '✅' : '❌';
      console.log(`   ${icon} ${table.padEnd(20)} : ${status}`);
      if (status !== 'OK') allOk = false;
    }

    console.log('\n🎯 CONCLUSION :');
    if (allOk) {
      console.log('   ✅ Toutes les souscriptions temps réel fonctionnent parfaitement !');
      console.log('   🚀 Le rafraîchissement automatique devrait fonctionner entre les profils.');
    } else {
      console.log('   ⚠️  Certaines souscriptions ne fonctionnent pas.');
      console.log('   📝 Exécutez le script enable-realtime.js pour les activer.');
    }

  } catch (error) {
    console.error('\n❌ Erreur lors du test:', error.message);
    console.log('\n💡 Solutions possibles :');
    console.log('   1. Vérifiez vos variables d\'environnement dans .env');
    console.log('   2. Vérifiez votre connexion internet');
    console.log('   3. Exécutez le script enable-realtime.js');
    process.exit(1);
  }
}

// Exécuter le test
testRealtime();
