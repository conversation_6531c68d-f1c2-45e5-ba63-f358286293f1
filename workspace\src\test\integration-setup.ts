import { beforeAll, afterAll, afterEach } from 'vitest'
import { server } from './mocks/server'

// Setup MSW pour les tests d'intégration
beforeAll(() => {
  // Démarre le serveur MSW pour intercepter les requêtes
  server.listen({ onUnhandledRequest: 'error' })
})

afterEach(() => {
  // Reset des handlers après chaque test
  server.resetHandlers()
})

afterAll(() => {
  // Arrêt du serveur MSW
  server.close()
})

// Configuration spécifique aux tests d'intégration
process.env.NODE_ENV = 'test' 