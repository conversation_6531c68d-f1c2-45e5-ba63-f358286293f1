/**
 * Script de test pour vérifier que l'assignation automatique fonctionne
 * Ce script vérifie que <PERSON> et <PERSON> sont bien assignés aux étapes
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Charger les variables d'environnement depuis le fichier .env
function loadEnv() {
  const envPath = path.join(__dirname, '..', '.env');
  if (!fs.existsSync(envPath)) {
    console.error('Fichier .env non trouvé');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      env[key.trim()] = value.trim().replace(/^["']|["']$/g, '');
    }
  });
  
  return env;
}

async function testAutoAssignment() {
  try {
    console.log('🧪 Test de l\'assignation automatique de Patrick et Quentin...\n');

    const env = loadEnv();
    const supabaseUrl = env.VITE_SUPABASE_URL;
    const supabaseKey = env.VITE_SUPABASE_API_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Variables d\'environnement Supabase manquantes dans .env');
    }

    console.log('📡 Connexion à Supabase...');
    const supabase = createClient(supabaseUrl, supabaseKey);

    // IDs de Patrick et Quentin
    const patrickId = '562a92cf-519b-4854-9749-f9dd90fe5776';
    const quentinId = '95a4a571-9940-4206-a22b-7004f6c77d43';

    // 1. Compter le nombre total d'étapes
    console.log('\n📊 Analyse des étapes et assignations...');
    const { data: stepsData, error: stepsError } = await supabase
      .from('steps')
      .select('id');

    if (stepsError) {
      throw new Error(`Erreur lors de la récupération des étapes: ${stepsError.message}`);
    }

    console.log(`   📈 Total des étapes: ${stepsData.length}`);

    // 2. Compter les assignations de Patrick
    const { data: patrickAssignments, error: patrickError } = await supabase
      .from('step_assignments')
      .select('step_id')
      .eq('assignee_id', patrickId);

    if (patrickError) {
      throw new Error(`Erreur lors de la récupération des assignations de Patrick: ${patrickError.message}`);
    }

    // 3. Compter les assignations de Quentin
    const { data: quentinAssignments, error: quentinError } = await supabase
      .from('step_assignments')
      .select('step_id')
      .eq('assignee_id', quentinId);

    if (quentinError) {
      throw new Error(`Erreur lors de la récupération des assignations de Quentin: ${quentinError.message}`);
    }

    // 4. Analyser les résultats
    console.log('\n📋 RÉSULTATS DE L\'ASSIGNATION AUTOMATIQUE :');
    console.log('==============================================');
    console.log(`   👤 Patrick assigné à: ${patrickAssignments.length} étapes`);
    console.log(`   👤 Quentin assigné à: ${quentinAssignments.length} étapes`);
    console.log(`   📊 Total des étapes: ${stepsData.length}`);

    // 5. Calculer les pourcentages
    const patrickPercentage = ((patrickAssignments.length / stepsData.length) * 100).toFixed(1);
    const quentinPercentage = ((quentinAssignments.length / stepsData.length) * 100).toFixed(1);

    console.log(`\n📈 COUVERTURE :`);
    console.log(`   👤 Patrick: ${patrickPercentage}% des étapes`);
    console.log(`   👤 Quentin: ${quentinPercentage}% des étapes`);

    // 6. Vérifier si l'assignation automatique fonctionne bien
    const expectedAssignments = stepsData.length;
    const patrickOk = patrickAssignments.length >= expectedAssignments * 0.9; // Au moins 90%
    const quentinOk = quentinAssignments.length >= expectedAssignments * 0.9; // Au moins 90%

    console.log('\n🎯 ÉVALUATION :');
    if (patrickOk && quentinOk) {
      console.log('   ✅ L\'assignation automatique fonctionne parfaitement !');
      console.log('   🚀 Patrick et Quentin sont bien assignés à la plupart des étapes.');
    } else {
      console.log('   ⚠️  L\'assignation automatique pourrait être améliorée :');
      if (!patrickOk) {
        console.log(`   ❌ Patrick n'est assigné qu'à ${patrickPercentage}% des étapes`);
      }
      if (!quentinOk) {
        console.log(`   ❌ Quentin n'est assigné qu'à ${quentinPercentage}% des étapes`);
      }
    }

    // 7. Vérifier quelques étapes spécifiques
    console.log('\n🔍 VÉRIFICATION DÉTAILLÉE (5 premières étapes) :');
    const firstSteps = stepsData.slice(0, 5);
    
    for (const step of firstSteps) {
      const { data: stepAssignments, error } = await supabase
        .from('step_assignments')
        .select('assignee_id')
        .eq('step_id', step.id);

      if (!error) {
        const hasPatrick = stepAssignments.some(a => a.assignee_id === patrickId);
        const hasQuentin = stepAssignments.some(a => a.assignee_id === quentinId);
        
        const patrickIcon = hasPatrick ? '✅' : '❌';
        const quentinIcon = hasQuentin ? '✅' : '❌';
        
        console.log(`   Étape ${step.id.substring(0, 8)}... : Patrick ${patrickIcon} | Quentin ${quentinIcon}`);
      }
    }

    console.log('\n🎉 Test terminé !');

  } catch (error) {
    console.error('\n❌ Erreur lors du test:', error.message);
    process.exit(1);
  }
}

// Exécuter le test
testAutoAssignment();
