/**
 * Service pour la gestion des données clients
 * Centralise toutes les opérations CRUD sur les clients
 */

import { supabase } from "@/integrations/supabase/client";
import { Client, SupabaseClient, SupabaseStep, SupabaseAssignee, SupabaseStepAssignment } from '@/types';
import { handleError, ErrorCode, createAppError } from "@/services/errorService";
import { logCreate, logRead, logUpdate, logDelete } from "@/utils/secure-logging";
import { encryptText, safeDecryptText, getEncryptionKey } from "@/utils/encryption";
import { defaultSteps } from '@/data/defaultSteps';

export class ClientDataService {
  /**
   * Récupère tous les clients avec leurs étapes depuis la base de données
   */
  static async getAllClients(currentProfileId?: string): Promise<Client[]> {
    try {
      // Journaliser l'opération
      logRead('clients', null, currentProfileId, 'Chargement des clients');

      // Récupérer les clients
      const { data: clientsData, error: clientsError } = await supabase
        .from('clients')
        .select('id, name, due_date, completed, created_at');

      if (clientsError) {
        throw createAppError(
          `Erreur lors du chargement des clients: ${clientsError.message}`,
          ErrorCode.DB_QUERY_ERROR
        );
      }

      // Récupérer les étapes
      const { data: stepsData, error: stepsError } = await supabase
        .from('steps')
        .select('*');

      if (stepsError) {
        throw createAppError(
          `Erreur lors du chargement des étapes: ${stepsError.message}`,
          ErrorCode.DB_QUERY_ERROR
        );
      }

      if (!clientsData || clientsData.length === 0) {
        return [];
      }

      return await this.mapSupabaseDataToClients(
        clientsData as SupabaseClient[],
        stepsData as SupabaseStep[]
      );
    } catch (error) {
      handleError(error, 'ClientDataService.getAllClients');
      return [];
    }
  }

  /**
   * Crée un nouveau client avec les étapes par défaut
   */
  static async createClient(name: string, currentProfileId?: string): Promise<Client | null> {
    try {
      // Chiffrer le nom du client
      const encryptionKey = getEncryptionKey();
      const encryptedName = await encryptText(name, encryptionKey);
      console.log('Nom du client chiffré pour protection des données sensibles');

      // Journaliser l'opération
      logCreate('clients', null, currentProfileId, `Création du client: ${name}`);

      // Créer le client
      const { data: newClient, error: clientError } = await supabase
        .from('clients')
        .insert({ name: encryptedName })
        .select()
        .single();

      if (clientError) throw clientError;

      // Créer les étapes par défaut
      const stepsToInsert = await Promise.all(defaultSteps.map(async stepName => {
        const encryptedStepName = await encryptText(stepName, encryptionKey);
        console.log(`Nom de l'étape par défaut "${stepName}" chiffré`);

        return {
          client_id: newClient.id,
          name: encryptedStepName,
          status: 'manquant' as const,
          received_date: null,
          comment: ''
        };
      }));

      const { data: newSteps, error: stepsError } = await supabase
        .from('steps')
        .insert(stepsToInsert)
        .select();

      if (stepsError) throw stepsError;

      // Retourner le client avec ses étapes
      return {
        id: newClient.id,
        name: name, // Nom en clair pour l'affichage
        dueDate: null,
        completed: false,
        steps: (newSteps as SupabaseStep[]).map((step, index) => ({
          id: step.id,
          name: defaultSteps[index],
          status: step.status,
          receivedDate: step.received_date ? new Date(step.received_date).toISOString().split('T')[0] : null,
          comment: step.comment || '',
          assignees: []
        }))
      };
    } catch (error) {
      handleError(error, 'ClientDataService.createClient');
      return null;
    }
  }

  /**
   * Supprime un client et toutes ses données associées
   */
  static async deleteClient(clientId: string, currentProfileId?: string): Promise<boolean> {
    try {
      // Journaliser l'opération
      logDelete('clients', clientId, currentProfileId, 'Suppression du client');

      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', clientId);

      if (error) throw error;

      return true;
    } catch (error) {
      handleError(error, 'ClientDataService.deleteClient');
      return false;
    }
  }

  /**
   * Met à jour le nom d'un client
   */
  static async updateClientName(clientId: string, name: string, currentProfileId?: string): Promise<boolean> {
    try {
      // Chiffrer le nouveau nom
      const encryptionKey = getEncryptionKey();
      const encryptedName = await encryptText(name, encryptionKey);
      console.log('Nom du client chiffré pour protection des données sensibles');

      // Journaliser l'opération
      logUpdate('clients', clientId, currentProfileId, 'Mise à jour du nom');

      const { error } = await supabase
        .from('clients')
        .update({ name: encryptedName })
        .eq('id', clientId);

      if (error) throw error;

      return true;
    } catch (error) {
      handleError(error, 'ClientDataService.updateClientName');
      return false;
    }
  }

  /**
   * Met à jour la date de rendu d'un client
   */
  static async updateClientDueDate(clientId: string, dueDate: string | null, currentProfileId?: string): Promise<boolean> {
    try {
      // Journaliser l'opération
      logUpdate('clients', clientId, currentProfileId, 'Mise à jour de la date de rendu');

      // Formater la date
      const formattedDate = dueDate ? new Date(dueDate).toISOString().split('T')[0] : null;

      const { error } = await supabase
        .from('clients')
        .update({ due_date: formattedDate })
        .eq('id', clientId);

      if (error) throw error;

      return true;
    } catch (error) {
      handleError(error, 'ClientDataService.updateClientDueDate');
      return false;
    }
  }

  /**
   * Met à jour le statut de completion d'un client
   */
  static async updateClientCompleted(clientId: string, completed: boolean, currentProfileId?: string): Promise<boolean> {
    try {
      // Journaliser l'opération
      logUpdate('clients', clientId, currentProfileId, 'Mise à jour du statut de completion');

      const { error } = await supabase
        .from('clients')
        .update({ completed })
        .eq('id', clientId);

      if (error) throw error;

      return true;
    } catch (error) {
      handleError(error, 'ClientDataService.updateClientCompleted');
      return false;
    }
  }

  /**
   * Duplique un client avec toutes ses étapes
   */
  static async duplicateClient(sourceClient: Client, currentProfileId?: string): Promise<Client | null> {
    try {
      // Chiffrer le nom du client dupliqué
      const encryptionKey = getEncryptionKey();
      const newName = `${sourceClient.name} (copie)`;
      const encryptedName = await encryptText(newName, encryptionKey);
      console.log('Nom du client dupliqué chiffré pour protection des données sensibles');

      // Journaliser l'opération
      logCreate('clients', null, currentProfileId, `Duplication du client: ${sourceClient.name}`);

      // Créer le nouveau client
      const { data: newClient, error: clientError } = await supabase
        .from('clients')
        .insert({ 
          name: encryptedName,
          due_date: sourceClient.dueDate,
          completed: false
        })
        .select()
        .single();

      if (clientError) throw clientError;

      // Dupliquer les étapes
      const stepsToInsert = await Promise.all(sourceClient.steps.map(async step => {
        const encryptedStepName = await encryptText(step.name, encryptionKey);
        console.log(`Nom de l'étape "${step.name}" chiffré pour la duplication`);

        return {
          client_id: newClient.id,
          name: encryptedStepName,
          status: step.status,
          received_date: step.receivedDate,
          comment: step.comment
        };
      }));

      const { data: newSteps, error: stepsError } = await supabase
        .from('steps')
        .insert(stepsToInsert)
        .select();

      if (stepsError) throw stepsError;

      // Retourner le client dupliqué
      return {
        id: newClient.id,
        name: newName,
        dueDate: sourceClient.dueDate,
        completed: false,
        steps: (newSteps as SupabaseStep[]).map((step, index) => ({
          id: step.id,
          name: sourceClient.steps[index]?.name || "Étape sans nom",
          status: step.status,
          receivedDate: step.received_date ? new Date(step.received_date).toISOString().split('T')[0] : null,
          comment: step.comment || '',
          assignees: [] // Les assignations ne sont pas dupliquées
        }))
      };
    } catch (error) {
      handleError(error, 'ClientDataService.duplicateClient');
      return null;
    }
  }

  /**
   * Mappe les données Supabase vers le format Client
   */
  private static async mapSupabaseDataToClients(
    clientsData: SupabaseClient[], 
    stepsData: SupabaseStep[]
  ): Promise<Client[]> {
    // Récupérer les assignations et assignees
    const { data: assignmentsData } = await supabase
      .from('step_assignments')
      .select('*');

    const { data: assigneesData } = await supabase
      .from('assignees')
      .select('*');

    const assignees = assigneesData as SupabaseAssignee[] || [];
    const assignments = assignmentsData as SupabaseStepAssignment[] || [];
    const encryptionKey = getEncryptionKey();

    // Traiter chaque client
    return await Promise.all(clientsData.map(async client => {
      // Déchiffrer le nom du client
      const clientName = await safeDecryptText(client.name, encryptionKey);

      // Traiter les étapes du client
      const clientSteps = stepsData.filter(step => step.client_id === client.id);
      const processedSteps = await Promise.all(clientSteps.map(async step => {
        // Déchiffrer les données de l'étape
        const stepName = await safeDecryptText(step.name, encryptionKey);
        const processedComment = await safeDecryptText(step.comment || '', encryptionKey);

        // Récupérer les assignations pour cette étape
        const stepAssignments = await Promise.all(
          assignments
            .filter(assignment => assignment.step_id === step.id)
            .map(async assignment => {
              const assignee = assignees.find(a => a.id === assignment.assignee_id);
              if (!assignee) return undefined;

              const assigneeName = await safeDecryptText(assignee.name, encryptionKey);
              return {
                id: assignee.id,
                name: assigneeName,
                createdAt: assignee.created_at
              };
            })
        );

        return {
          id: step.id,
          name: stepName,
          status: step.status,
          receivedDate: step.received_date ? new Date(step.received_date).toISOString().split('T')[0] : null,
          comment: processedComment,
          assignees: stepAssignments.filter(Boolean)
        };
      }));

      return {
        id: client.id,
        name: clientName,
        dueDate: client.due_date ? new Date(client.due_date).toISOString().split('T')[0] : null,
        completed: client.completed ?? false,
        steps: processedSteps
      };
    }));
  }
}
