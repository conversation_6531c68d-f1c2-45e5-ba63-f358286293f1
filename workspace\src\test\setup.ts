import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock des API Tauri
const mockTauri = {
  notification: {
    requestPermission: vi.fn(),
    sendNotification: vi.fn()
  },
  window: {
    getCurrent: vi.fn().mockReturnValue({
      minimize: vi.fn(),
      maximize: vi.fn(),
      close: vi.fn()
    })
  },
  http: {
    fetch: vi.fn()
  }
}

vi.mock('@tauri-apps/api/notification', () => mockTauri.notification)
vi.mock('@tauri-apps/api/window', () => mockTauri.window)
vi.mock('@tauri-apps/api/http', () => mockTauri.http)

// Mock de Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      signIn: vi.fn(),
      signOut: vi.fn(),
      getUser: vi.fn()
    },
    from: vi.fn().mockReturnValue({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null })
    })
  }
}))

// Mock des variables d'environnement
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_SUPABASE_URL: 'http://localhost:54321',
    VITE_SUPABASE_API_KEY: 'test-key',
    VITE_APP_NAME: 'WeMa Tracker Test',
    VITE_ENVIRONMENT: 'test'
  },
  writable: true
})

// Configuration globale des tests
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

global.matchMedia = vi.fn().mockImplementation(query => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}))

// Mock de l'API Intersection Observer
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
})) 