import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'

// Handlers de base pour Supabase
const handlers = [
  // Mock des endpoints Supabase Auth
  http.post('*/auth/v1/token', () => {
    return HttpResponse.json({
      access_token: 'test-token',
      refresh_token: 'test-refresh-token',
      user: {
        id: 'test-user-id',
        email: '<EMAIL>'
      }
    })
  }),

  // Mock des endpoints REST API
  http.get('*/rest/v1/clients', () => {
    return HttpResponse.json([
      {
        id: '1',
        name: 'Client Test',
        email: '<EMAIL>',
        created_at: new Date().toISOString()
      }
    ])
  }),

  http.post('*/rest/v1/clients', () => {
    return HttpResponse.json({
      id: '2',
      name: 'Nouveau Client',
      email: '<EMAIL>',
      created_at: new Date().toISOString()
    })
  }),

  // Mock des endpoints de profils
  http.get('*/rest/v1/profiles', () => {
    return HttpResponse.json([
      {
        id: '1',
        name: 'Profil Test',
        user_id: 'test-user-id',
        created_at: new Date().toISOString()
      }
    ])
  })
]

// Configuration du serveur MSW
export const server = setupServer(...handlers) 