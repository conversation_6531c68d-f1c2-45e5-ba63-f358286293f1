
/**
 * Types et interfaces pour l'application
 */

// Types communs
export type Status = 'manquant' | 'transmis' | 'validation' | 'valide' | 'refuse' | 'urgent';
export type UUID = string;
export type ISODateString = string;

// Interfaces pour les modèles de données utilisés dans l'application
export interface Assignee {
  id: UUID;
  name: string;
  createdAt?: ISODateString;
}

export interface Step {
  id: UUID;
  name: string;
  status: Status;
  receivedDate: ISODateString | null;
  comment: string;
  assignees: Assignee[];
}

export interface Client {
  id: UUID;
  name: string;
  dueDate: ISODateString | null; // Date de rendu du dossier
  completed: boolean; // Marqué comme terminé manuellement
  steps: Step[];
  created_at?: ISODateString;
}

export interface Profile {
  id: UUID;
  name: string;
  avatar_url?: string;
  created_at?: ISODateString;
}

export interface Notification {
  id: UUID;
  user_id: UUID;
  step_id: UUID;
  created_by: UUID;
  message: string;
  read: boolean;
  created_at: ISODateString;
  created_by_profile?: {
    name: string;
  };
}

export interface Ping {
  id: UUID;
  fromUserId: UUID;
  toUserId: UUID;
  message: string;
  read: boolean;
  createdAt: ISODateString;
  fromUserName?: string;
  toUserName?: string;
}

// Interfaces pour les modèles de données Supabase (tables)
export interface SupabaseClient {
  id: UUID;
  name: string;
  due_date: ISODateString | null; // Date de rendu du dossier
  completed: boolean | null; // Marqué comme terminé manuellement (nullable en base)
  created_at?: ISODateString;
}

export interface SupabaseStep {
  id: UUID;
  client_id: UUID;
  name: string;
  status: Status;
  received_date: ISODateString | null;
  comment: string;
  created_at?: ISODateString;
}

export interface SupabaseAssignee {
  id: UUID;
  name: string;
  created_at?: ISODateString;
}

export interface SupabaseStepAssignment {
  id: UUID;
  step_id: UUID;
  assignee_id: UUID;
  is_urgent_notified: boolean;
  created_at?: ISODateString;
}

export interface SupabaseNotification {
  id: UUID;
  user_id: UUID;
  step_id: UUID;
  created_by: UUID;
  message: string;
  read: boolean;
  created_at: ISODateString;
}

export interface SupabasePing {
  id: UUID;
  from_user_id: UUID;
  to_user_id: UUID;
  message: string;
  read: boolean;
  created_at: ISODateString;
}

export interface SupabaseComplianceDocument {
  id: UUID;
  client_id: UUID;
  document_type: string;
  status: 'manquant' | 'transmis' | 'signe_valide';
  created_at?: ISODateString;
  updated_at?: ISODateString;
}

// Types pour les réponses d'API
export interface ApiResponse<T> {
  data: T | null;
  error: Error | null;
}

// Types pour la gestion des erreurs
export interface AppError extends Error {
  code?: string;
  details?: string;
  hint?: string;
}

// Fonctions utilitaires pour la conversion de types
export function supabaseClientToClient(supabaseClient: SupabaseClient, steps: Step[] = []): Client {
  return {
    id: supabaseClient.id,
    name: supabaseClient.name,
    dueDate: supabaseClient.due_date,
    completed: supabaseClient.completed ?? false, // Convertir null en false
    steps,
    created_at: supabaseClient.created_at
  };
}

export function clientToSupabaseClient(client: Client): Omit<SupabaseClient, 'created_at'> {
  return {
    id: client.id,
    name: client.name,
    due_date: client.dueDate,
    completed: client.completed
  };
}
