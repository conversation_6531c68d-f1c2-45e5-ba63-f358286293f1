/**
 * Hook personnalisé pour centraliser les opérations de chiffrement et déchiffrement
 * Fournit des fonctions pour chiffrer et déchiffrer des données sensibles
 */

import { useState, useCallback } from 'react';
import { encryptText, decryptText, getEncryptionKey, isEncrypted } from "@/utils/encryption";
import { handleError, ErrorCode, createAppError } from "@/services/errorService";

/**
 * Hook pour gérer le chiffrement et déchiffrement des données
 * @returns Fonctions pour chiffrer et déchiffrer des données
 */
export function useEncryption() {
  // Récupérer la clé de chiffrement une seule fois
  const [encryptionKey] = useState(getEncryptionKey);
  
  /**
   * Chiffre une chaîne de texte
   * @param text - Texte à chiffrer
   * @returns Texte chiffré
   */
  const encrypt = useCallback(async (text: string): Promise<string> => {
    if (!text || isEncrypted(text)) return text;
    
    try {
      return await encryptText(text, encryptionKey);
    } catch (error) {
      handleError(
        createAppError(
          `Erreur lors du chiffrement: ${(error as Error).message}`,
          ErrorCode.ENCRYPTION_ERROR
        ),
        'useEncryption.encrypt'
      );
      return text; // Fallback en cas d'erreur
    }
  }, [encryptionKey]);
  
  /**
   * Déchiffre une chaîne de texte
   * @param text - Texte à déchiffrer
   * @returns Texte déchiffré
   */
  const decrypt = useCallback(async (text: string): Promise<string> => {
    if (!text || !isEncrypted(text)) return text;
    
    try {
      return await decryptText(text, encryptionKey);
    } catch (error) {
      handleError(
        createAppError(
          `Erreur lors du déchiffrement: ${(error as Error).message}`,
          ErrorCode.DECRYPTION_ERROR
        ),
        'useEncryption.decrypt'
      );
      return text; // Fallback en cas d'erreur
    }
  }, [encryptionKey]);
  
  /**
   * Déchiffre plusieurs champs dans un tableau d'objets
   * @param objects - Tableau d'objets à traiter
   * @param fields - Champs à déchiffrer dans chaque objet
   * @returns Tableau d'objets avec les champs déchiffrés
   */
  const batchDecrypt = useCallback(async <T extends Record<string, any>>(
    objects: T[],
    fields: (keyof T)[]
  ): Promise<T[]> => {
    return Promise.all(
      objects.map(async (obj) => {
        const newObj = { ...obj };
        for (const field of fields) {
          if (typeof newObj[field] === 'string' && isEncrypted(newObj[field] as string)) {
            newObj[field] = await decrypt(newObj[field] as string) as any;
          }
        }
        return newObj;
      })
    );
  }, [decrypt]);
  
  return { 
    encrypt, 
    decrypt, 
    batchDecrypt,
    isEncrypted 
  };
}
