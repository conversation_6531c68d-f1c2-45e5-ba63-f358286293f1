/**
 * Script pour appliquer les migrations SQL à la base de données Supabase
 * Ce script est exécuté au démarrage de l'application pour s'assurer que
 * la base de données est à jour avec les dernières modifications.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Charger les variables d'environnement
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_API_KEY = process.env.VITE_SUPABASE_API_KEY;

// Vérifier que les variables d'environnement sont définies
if (!SUPABASE_URL || !SUPABASE_API_KEY) {
  console.error('Les variables d\'environnement VITE_SUPABASE_URL et VITE_SUPABASE_API_KEY doivent être définies');
  process.exit(1);
}

// Créer un client Supabase
const supabase = createClient(SUPABASE_URL, SUPABASE_API_KEY);

// Chemin vers le dossier des migrations
const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');

// Fonction pour appliquer une migration
async function applyMigration(filePath) {
  try {
    console.log(`Application de la migration: ${path.basename(filePath)}`);
    
    // Lire le contenu du fichier SQL
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Exécuter le SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error(`Erreur lors de l'application de la migration ${path.basename(filePath)}:`, error);
      return false;
    }
    
    console.log(`Migration ${path.basename(filePath)} appliquée avec succès`);
    return true;
  } catch (error) {
    console.error(`Erreur lors de l'application de la migration ${path.basename(filePath)}:`, error);
    return false;
  }
}

// Fonction principale
async function main() {
  try {
    // Vérifier si le dossier des migrations existe
    if (!fs.existsSync(migrationsDir)) {
      console.error(`Le dossier des migrations n'existe pas: ${migrationsDir}`);
      process.exit(1);
    }
    
    // Lire les fichiers de migration
    const files = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Trier par ordre alphabétique
    
    if (files.length === 0) {
      console.log('Aucun fichier de migration trouvé');
      process.exit(0);
    }
    
    console.log(`${files.length} fichiers de migration trouvés`);
    
    // Appliquer les migrations
    let success = true;
    for (const file of files) {
      const filePath = path.join(migrationsDir, file);
      const result = await applyMigration(filePath);
      if (!result) {
        success = false;
        break;
      }
    }
    
    if (success) {
      console.log('Toutes les migrations ont été appliquées avec succès');
    } else {
      console.error('Certaines migrations ont échoué');
      process.exit(1);
    }
  } catch (error) {
    console.error('Erreur lors de l\'application des migrations:', error);
    process.exit(1);
  }
}

// Exécuter la fonction principale
main();
