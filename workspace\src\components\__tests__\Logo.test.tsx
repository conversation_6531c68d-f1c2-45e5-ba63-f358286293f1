import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { Logo } from '../Logo'

describe('Logo', () => {
  it('should render the logo image', () => {
    render(<Logo />)
    
    const logoImage = screen.getByRole('img', { name: /wema tracker/i })
    expect(logoImage).toBeInTheDocument()
  })

  it('should have correct alt text', () => {
    render(<Logo />)
    
    const logoImage = screen.getByRole('img')
    expect(logoImage).toHaveAttribute('alt', expect.stringContaining('WeMa Tracker'))
  })

  it('should render without crashing', () => {
    expect(() => render(<Logo />)).not.toThrow()
  })
}) 