import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "next-themes";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ClientProvider } from "./contexts/ClientContext";
import { ProfileProvider } from "./contexts/ProfileContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import { PingProvider } from "./contexts/PingContext";
import { Logo } from "./components/Logo";
import Index from "./pages/Index";
import ClientDetail from "./pages/ClientDetail";
import NotFound from "./pages/NotFound";
import CreateProfile from "./pages/CreateProfile";
import { useEffect } from "react";
import { setupKeepAlive } from "./utils/keep-alive";
import CustomTitlebar from "./components/CustomTitlebar";

const queryClient = new QueryClient();

const App = () => {
  // Configurer les métadonnées du document
  useEffect(() => {
    // Définir le favicon
    const link = document.querySelector("link[rel~='icon']") as HTMLLinkElement;
    if (link) {
      link.href = "/lovable-uploads/wema-logo.png";
    }

    // Définir le titre de la page
    document.title = "WeMa Tracker";

    // Setup notification check for desktop app icon
    const checkUrgentTasks = () => {
      // This is just to set up the app icon
      // Actual notifications will be handled in the ClientContext
      const appIconPath = "/lovable-uploads/wema-logo.png";
      return appIconPath;
    };

    // Initial check for app icon
    checkUrgentTasks();

    // Configurer le système de keep-alive pour Supabase
    setupKeepAlive();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false} forcedTheme="light">
        <ProfileProvider>
          <NotificationProvider>
            <PingProvider>
              <ClientProvider>
                <TooltipProvider>
                  {/* Composant pour personnaliser la barre de titre Tauri */}
                  <CustomTitlebar />

                  <div className="min-h-screen bg-background text-foreground relative">
                    <header className="absolute top-4 left-4 z-50 lg:hidden">
                      <Logo size="sm" className="opacity-80 hover:opacity-100 transition-opacity" />
                    </header>
                    <Toaster />
                    <BrowserRouter>
                      <Routes>
                        <Route path="/" element={<Index />} />
                        <Route path="/client/:id" element={<ClientDetail />} />
                        <Route path="/profile/new" element={<CreateProfile />} />
                        <Route path="*" element={<NotFound />} />
                      </Routes>
                    </BrowserRouter>
                  </div>
                </TooltipProvider>
              </ClientProvider>
            </PingProvider>
          </NotificationProvider>
        </ProfileProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
