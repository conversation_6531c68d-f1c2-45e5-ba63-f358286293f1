import { supabase } from "@/integrations/supabase/client";
import { Profile } from "@/types";

// Utiliser les variables d'environnement pour les clés sensibles
const SUPABASE_API_KEY = import.meta.env.VITE_SUPABASE_API_KEY;
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;

// Fonction pour créer un profil directement avec supabase (utilise la table assignees)
export const createProfileWithFetch = async (name: string): Promise<Profile | null> => {
  try {
    console.log('Tentative de création de profil dans la table assignees pour:', name);

    // Importer les fonctions de chiffrement
    const { encryptText, getEncryptionKey } = await import('@/utils/encryption');

    // Chiffrer le nom du profil pour protéger les données sensibles
    const encryptionKey = getEncryptionKey();
    const encryptedName = await encryptText(name, encryptionKey);
    console.log('Nom du profil chiffré pour protection des données sensibles');

    // Utiliser directement le client supabase pour insérer dans la table assignees
    const { data, error } = await supabase
      .from('assignees')
      .insert({ name: encryptedName })
      .select()
      .single();

    if (error) {
      console.error('Erreur lors de la création du profil dans assignees:', error);
      throw error;
    }

    console.log('Profil créé avec succès dans la table assignees:', data.id);

    // Convertir l'assignee en profil (utiliser le nom non chiffré pour l'interface)
    const profile: Profile = {
      id: data.id,
      name: name, // Utiliser le nom original non chiffré pour l'affichage
      created_at: data.created_at
    };

    return profile;
  } catch (error) {
    console.error('Erreur lors de la création du profil:', error);
    return null;
  }
};

// Fonction pour créer un ping direct entre utilisateurs (utilise la table pings)
export const createPing = async (
  fromUserId: string,
  toUserId: string,
  message: string
): Promise<boolean> => {
  try {
    console.log('Création d\'un ping de', fromUserId, 'à', toUserId);

    // Importer les fonctions de chiffrement
    const { encryptText, getEncryptionKey } = await import('@/utils/encryption');

    // Chiffrer le message pour protéger la confidentialité des communications
    const encryptionKey = getEncryptionKey();
    const encryptedMessage = await encryptText(message, encryptionKey);
    console.log('Message chiffré pour protection de la confidentialité');

    const { error } = await supabase
      .from('pings')
      .insert({
        from_user_id: fromUserId,
        to_user_id: toUserId,
        message: encryptedMessage,
        read: false
      });

    if (error) {
      console.error('Erreur lors de la création du ping:', error);
      throw error;
    }

    console.log('Ping créé avec succès');
    return true;
  } catch (error) {
    console.error('Error creating ping:', error);
    return false;
  }
};

// Fonction pour récupérer les pings d'un utilisateur (utilise la table pings)
export const getUserPings = async (userId: string) => {
  try {
    console.log('Récupération des pings pour l\'utilisateur:', userId);

    // Récupérer les pings
    const { data, error } = await supabase
      .from('pings')
      .select('*')
      .eq('to_user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erreur lors de la récupération des pings:', error);
      throw error;
    }

    // Importer les fonctions de déchiffrement
    const { decryptText, getEncryptionKey, isEncrypted } = await import('@/utils/encryption');
    const encryptionKey = getEncryptionKey();

    // Récupérer les noms des expéditeurs
    const fromUserIds = [...new Set(data?.map(ping => ping.from_user_id) || [])];
    const senderNames: Record<string, string> = {};

    if (fromUserIds.length > 0) {
      const { data: assigneesData, error: assigneesError } = await supabase
        .from('assignees')
        .select('id, name')
        .in('id', fromUserIds);

      if (!assigneesError && assigneesData) {
        // Traiter chaque assignee et déchiffrer le nom si nécessaire
        // (pour gérer les noms qui ont été chiffrés avant la modification)
        for (const assignee of assigneesData) {
          let name = assignee.name;
          // Vérifier si le nom est chiffré avant de tenter de le déchiffrer
          if (isEncrypted(name)) {
            try {
              name = await decryptText(name, encryptionKey);
              console.log(`Nom de l'expéditeur ${assignee.id} déchiffré (données existantes)`);
            } catch (decryptError) {
              console.error('Erreur lors du déchiffrement du nom:', decryptError);
              // Conserver le nom chiffré en cas d'erreur
            }
          }
          senderNames[assignee.id] = name;
        }
      }
    }

    console.log('Pings récupérés avec succès:', data?.length || 0);

    // Déchiffrer les messages et formater les pings
    const decryptedPings = await Promise.all((data || []).map(async ping => {
      let decryptedMessage = ping.message;

      // Vérifier si le message est chiffré avant de tenter de le déchiffrer
      if (isEncrypted(ping.message)) {
        try {
          decryptedMessage = await decryptText(ping.message, encryptionKey);
        } catch (decryptError) {
          console.error('Erreur lors du déchiffrement du message:', decryptError);
          // Conserver le message chiffré en cas d'erreur
        }
      }

      return {
        id: ping.id,
        from_user_id: ping.from_user_id,
        to_user_id: ping.to_user_id,
        message: decryptedMessage,
        read: ping.read,
        created_at: ping.created_at,
        from_user: {
          name: senderNames[ping.from_user_id] || 'Utilisateur inconnu'
        }
      };
    }));

    return decryptedPings;
  } catch (error) {
    console.error('Error fetching pings:', error);
    return [];
  }
};

// Fonction pour marquer un ping comme lu (utilise la table pings)
// Note: Cette fonction ne marque le ping comme lu que pour le profil courant
// en utilisant une table de lecture séparée
export const markPingAsRead = async (pingId: string, currentProfileId: string): Promise<boolean> => {
  try {
    console.log('Marquage du ping comme lu pour le profil:', currentProfileId, 'ping:', pingId);

    // Vérifier si le ping existe et appartient au profil courant
    const { data: pingData, error: pingError } = await supabase
      .from('pings')
      .select('*')
      .eq('id', pingId)
      .eq('to_user_id', currentProfileId)
      .single();

    if (pingError) {
      console.error('Erreur lors de la vérification du ping:', pingError);
      throw pingError;
    }

    if (!pingData) {
      console.error('Le ping n\'existe pas ou n\'appartient pas au profil courant');
      return false;
    }

    // Marquer le ping comme lu uniquement pour ce profil
    const { error } = await supabase
      .from('pings')
      .update({ read: true })
      .eq('id', pingId)
      .eq('to_user_id', currentProfileId);

    if (error) {
      console.error('Erreur lors du marquage du ping comme lu:', error);
      throw error;
    }

    console.log('Ping marqué comme lu avec succès pour le profil:', currentProfileId);
    return true;
  } catch (error) {
    console.error('Error marking ping as read:', error);
    return false;
  }
};

// Fonction pour supprimer un profil (utilise la table assignees)
export const deleteProfile = async (profileId: string): Promise<boolean> => {
  try {
    console.log('Suppression du profil:', profileId);

    // Vérifier si le profil existe
    const { data: profileData, error: profileError } = await supabase
      .from('assignees')
      .select('id')
      .eq('id', profileId)
      .single();

    if (profileError) {
      console.error('Erreur lors de la vérification du profil:', profileError);
      return false;
    }

    if (!profileData) {
      console.error('Le profil n\'existe pas');
      return false;
    }

    // Supprimer les pings associés au profil
    const { error: pingsError } = await supabase
      .from('pings')
      .delete()
      .or(`from_user_id.eq.${profileId},to_user_id.eq.${profileId}`);

    if (pingsError) {
      console.error('Erreur lors de la suppression des pings:', pingsError);
      // Continuer malgré l'erreur
    }

    // Supprimer les notifications associées au profil
    const { error: notificationsError } = await supabase
      .from('notifications')
      .delete()
      .eq('user_id', profileId);

    if (notificationsError) {
      console.error('Erreur lors de la suppression des notifications:', notificationsError);
      // Continuer malgré l'erreur
    }

    // Supprimer les assignations d'étapes associées au profil
    const { error: assignmentsError } = await supabase
      .from('step_assignments')
      .delete()
      .eq('assignee_id', profileId);

    if (assignmentsError) {
      console.error('Erreur lors de la suppression des assignations d\'étapes:', assignmentsError);
      // Continuer malgré l'erreur
    }

    // Note: Nous n'utilisons plus la table profiles

    // Supprimer le profil de la table assignees
    const { error } = await supabase
      .from('assignees')
      .delete()
      .eq('id', profileId);

    if (error) {
      console.error('Erreur lors de la suppression du profil de la table assignees:', error);
      throw error;
    }

    console.log('Profil supprimé avec succès');
    return true;
  } catch (error) {
    console.error('Error deleting profile:', error);
    return false;
  }
};