/**
 * Utilitaire pour diagnostiquer et corriger les problèmes de données
 */

import { supabase } from "@/integrations/supabase/client";
import { getEncryptionKey, safeDecryptText, isEncrypted, encryptText } from "@/utils/encryption";

export interface AssigneeData {
  id: string;
  name: string;
  created_at: string;
  original_name?: string;
  is_encrypted?: boolean;
  has_issues?: boolean;
  fixed_name?: string;
}

/**
 * Diagnostique les données des assignees
 */
export async function diagnoseAssignees(): Promise<AssigneeData[]> {
  try {
    console.log('🔍 Diagnostic des assignees...');
    
    const { data: assigneesData, error } = await supabase
      .from('assignees')
      .select('*')
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    if (!assigneesData || assigneesData.length === 0) {
      console.log('ℹ️ Aucun assignee trouvé');
      return [];
    }

    const encryptionKey = getEncryptionKey();
    const diagnostics: AssigneeData[] = [];

    for (const assignee of assigneesData) {
      const diagnostic: AssigneeData = {
        id: assignee.id,
        name: assignee.name,
        created_at: assignee.created_at,
        original_name: assignee.name,
        is_encrypted: isEncrypted(assignee.name),
        has_issues: false
      };

      // Tester le déchiffrement
      try {
        const decryptedName = await safeDecryptText(assignee.name, encryptionKey);
        diagnostic.fixed_name = decryptedName;

        // Vérifier si le nom a des problèmes
        if (decryptedName !== assignee.name && !diagnostic.is_encrypted) {
          diagnostic.has_issues = true;
        }

        // Vérifier si le nom contient uniquement des chiffres
        if (/^[0-9]+$/.test(decryptedName)) {
          diagnostic.has_issues = true;
        }

        // Vérifier si le nom contient des caractères de contrôle
        if (/[\x00-\x1F\x7F-\x9F]/.test(decryptedName)) {
          diagnostic.has_issues = true;
        }

      } catch (error) {
        console.error(`Erreur lors du déchiffrement de l'assignee ${assignee.id}:`, error);
        diagnostic.has_issues = true;
        diagnostic.fixed_name = `Utilisateur ${assignee.id.substring(0, 8)}`;
      }

      diagnostics.push(diagnostic);
    }

    console.log('📊 Diagnostic terminé:', diagnostics);
    return diagnostics;

  } catch (error) {
    console.error('❌ Erreur lors du diagnostic:', error);
    throw error;
  }
}

/**
 * Affiche un rapport de diagnostic
 */
export function printDiagnosticReport(diagnostics: AssigneeData[]) {
  console.group('📋 Rapport de diagnostic des assignees');
  
  const totalAssignees = diagnostics.length;
  const encryptedAssignees = diagnostics.filter(d => d.is_encrypted).length;
  const problemAssignees = diagnostics.filter(d => d.has_issues).length;

  console.log(`Total: ${totalAssignees} assignees`);
  console.log(`Chiffrés: ${encryptedAssignees} assignees`);
  console.log(`Avec problèmes: ${problemAssignees} assignees`);

  if (problemAssignees > 0) {
    console.group('⚠️ Assignees avec problèmes:');
    diagnostics.filter(d => d.has_issues).forEach(assignee => {
      console.log(`- ID: ${assignee.id}`);
      console.log(`  Nom original: "${assignee.original_name}"`);
      console.log(`  Nom corrigé: "${assignee.fixed_name}"`);
      console.log(`  Est chiffré: ${assignee.is_encrypted}`);
    });
    console.groupEnd();
  }

  console.groupEnd();
}

/**
 * Corrige automatiquement les noms d'assignees problématiques
 */
export async function fixAssigneeNames(diagnostics: AssigneeData[]): Promise<number> {
  const problemAssignees = diagnostics.filter(d => d.has_issues);
  
  if (problemAssignees.length === 0) {
    console.log('✅ Aucun assignee à corriger');
    return 0;
  }

  console.log(`🔧 Correction de ${problemAssignees.length} assignees...`);
  
  const encryptionKey = getEncryptionKey();
  let fixedCount = 0;

  for (const assignee of problemAssignees) {
    try {
      let newName = assignee.fixed_name || `Utilisateur ${assignee.id.substring(0, 8)}`;
      
      // Si le nom corrigé est encore problématique, le remplacer par un nom par défaut
      if (/^[0-9]+$/.test(newName)) {
        newName = `Personne${Math.floor(Math.random() * 1000)}`;
      }

      // Chiffrer le nouveau nom
      const encryptedName = await encryptText(newName, encryptionKey);

      // Mettre à jour dans la base de données
      const { error } = await supabase
        .from('assignees')
        .update({ name: encryptedName })
        .eq('id', assignee.id);

      if (error) {
        console.error(`Erreur lors de la correction de l'assignee ${assignee.id}:`, error);
      } else {
        console.log(`✅ Assignee ${assignee.id} corrigé: "${assignee.original_name}" → "${newName}"`);
        fixedCount++;
      }

    } catch (error) {
      console.error(`Erreur lors de la correction de l'assignee ${assignee.id}:`, error);
    }
  }

  console.log(`🎉 ${fixedCount} assignees corrigés sur ${problemAssignees.length}`);
  return fixedCount;
}

/**
 * Fonction principale pour diagnostiquer et corriger les assignees
 */
export async function diagnoseAndFixAssignees(): Promise<void> {
  try {
    console.log('🚀 Lancement du diagnostic et de la correction des assignees...');
    
    // 1. Diagnostic
    const diagnostics = await diagnoseAssignees();
    printDiagnosticReport(diagnostics);

    // 2. Correction automatique si nécessaire
    const problemCount = diagnostics.filter(d => d.has_issues).length;
    if (problemCount > 0) {
      const fixedCount = await fixAssigneeNames(diagnostics);
      console.log(`✨ Diagnostic et correction terminés: ${fixedCount} assignees corrigés`);
    } else {
      console.log('✅ Aucun problème détecté, pas de correction nécessaire');
    }

  } catch (error) {
    console.error('❌ Erreur lors du diagnostic et de la correction:', error);
    throw error;
  }
} 