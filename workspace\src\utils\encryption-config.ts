/**
 * Configuration du chiffrement pour l'application WeMa Tracker
 * Définit clairement quelles données doivent être chiffrées pour la conformité RGPD
 */

/**
 * Configuration des données à chiffrer
 * Basée sur les exigences RGPD pour la protection des données personnelles
 */
export const ENCRYPTION_CONFIG = {
  // Données clients (données personnelles sensibles)
  clients: {
    name: true,        // Nom du client - OBLIGATOIRE (donnée personnelle)
    dueDate: false,    // Date de rendu - pas de donnée personnelle
    completed: false   // Statut - pas de donnée personnelle
  },
  
  // Données des étapes
  steps: {
    name: true,        // Nom de l'étape - peut contenir des infos sensibles
    status: false,     // Statut - pas de donnée personnelle
    comment: true,     // Commentaire - peut contenir des infos sensibles
    receivedDate: false // Date - pas de donnée personnelle
  },
  
  // Données des profils/assignees
  assignees: {
    name: false        // Nom des utilisateurs - PAS chiffré pour faciliter l'usage
  },
  
  // Communications
  notifications: {
    message: true      // Messages - peuvent contenir des infos sensibles
  },
  
  pings: {
    message: true      // Messages - peuvent contenir des infos sensibles
  }
} as const;

/**
 * Vérifie si un champ doit être chiffré selon la configuration
 * @param table - Nom de la table
 * @param field - Nom du champ
 * @returns true si le champ doit être chiffré
 */
export function shouldEncryptField(table: keyof typeof ENCRYPTION_CONFIG, field: string): boolean {
  const tableConfig = ENCRYPTION_CONFIG[table];
  if (!tableConfig) return false;
  
  return (tableConfig as any)[field] === true;
}

/**
 * Types pour la validation TypeScript
 */
export type EncryptableTable = keyof typeof ENCRYPTION_CONFIG;
export type EncryptableField<T extends EncryptableTable> = keyof typeof ENCRYPTION_CONFIG[T];
