/**
 * Service pour la gestion des profils utilisateurs
 * Contient les fonctions liées aux opérations sur les profils
 */

import { supabase } from "@/integrations/supabase/client";
import { Profile } from "@/types";

/**
 * Récupère tous les profils utilisateurs
 * @returns Liste des profils
 */
export async function getAllProfiles(): Promise<Profile[]> {
  try {
    // Récupérer les profils depuis la table assignees
    const { data: assigneesData, error: assigneesError } = await supabase
      .from('assignees')
      .select('*')
      .order('name', { ascending: true });
    
    if (assigneesError) throw assigneesError;
    
    // Les noms des profils ne sont pas chiffrés selon la configuration
    const profiles = assigneesData.map(assignee => ({
      id: assignee.id,
      name: assignee.name, // Pas de déchiffrement nécessaire
      created_at: assignee.created_at
    }));
    
    return profiles;
  } catch (error) {
    console.error('Erreur lors de la récupération des profils:', error);
    return [];
  }
}

/**
 * Crée un nouveau profil
 * @param name Nom du profil
 * @returns Le profil créé
 */
export async function createProfile(name: string): Promise<Profile | null> {
  try {
    // Les noms des profils ne sont pas chiffrés pour faciliter l'affichage et la recherche
    const { data, error } = await supabase
      .from('assignees')
      .insert({ name })
      .select()
      .single();
    
    if (error) throw error;
    
    return {
      id: data.id,
      name: data.name,
      created_at: data.created_at
    };
  } catch (error) {
    console.error('Erreur lors de la création du profil:', error);
    return null;
  }
}

/**
 * Supprime un profil
 * @param profileId ID du profil
 * @returns true si la suppression a réussi, false sinon
 */
export async function deleteProfile(profileId: string): Promise<boolean> {
  try {
    // Vérifier si le profil existe
    const { data: profileData, error: profileError } = await supabase
      .from('assignees')
      .select('id')
      .eq('id', profileId)
      .single();
    
    if (profileError) throw profileError;
    if (!profileData) throw new Error('Le profil n\'existe pas');
    
    // Supprimer les pings associés au profil
    const { error: pingsError } = await supabase
      .from('pings')
      .delete()
      .or(`from_user_id.eq.${profileId},to_user_id.eq.${profileId}`);
    
    if (pingsError) {
      console.error('Erreur lors de la suppression des pings:', pingsError);
      // Continuer malgré l'erreur
    }
    
    // Supprimer les notifications associées au profil
    const { error: notificationsError } = await supabase
      .from('notifications')
      .delete()
      .eq('user_id', profileId);
    
    if (notificationsError) {
      console.error('Erreur lors de la suppression des notifications:', notificationsError);
      // Continuer malgré l'erreur
    }
    
    // Supprimer les assignations d'étapes associées au profil
    const { error: assignmentsError } = await supabase
      .from('step_assignments')
      .delete()
      .eq('assignee_id', profileId);
    
    if (assignmentsError) {
      console.error('Erreur lors de la suppression des assignations d\'étapes:', assignmentsError);
      // Continuer malgré l'erreur
    }
    
    // Supprimer le profil
    const { error } = await supabase
      .from('assignees')
      .delete()
      .eq('id', profileId);
    
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error('Erreur lors de la suppression du profil:', error);
    return false;
  }
}

/**
 * Récupère un profil par son ID
 * @param profileId ID du profil
 * @returns Le profil ou null s'il n'existe pas
 */
export async function getProfileById(profileId: string): Promise<Profile | null> {
  try {
    const { data, error } = await supabase
      .from('assignees')
      .select('*')
      .eq('id', profileId)
      .single();
    
    if (error) throw error;
    
    // Les noms des profils ne sont pas chiffrés selon la configuration
    return {
      id: data.id,
      name: data.name, // Pas de déchiffrement nécessaire
      created_at: data.created_at
    };
  } catch (error) {
    console.error('Erreur lors de la récupération du profil:', error);
    return null;
  }
}
