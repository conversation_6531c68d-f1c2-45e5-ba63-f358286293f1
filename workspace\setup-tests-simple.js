#!/usr/bin/env node

/**
 * Script de Setup Tests Simplifié - WeMa Tracker
 * Placé à la racine pour faciliter l'accès
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Configuration simplifiée des tests WeMa Tracker...\n');

// Vérification de l'environnement
console.log('📍 Répertoire de travail:', process.cwd());
console.log('📍 Node.js version:', process.version);

// Fonction pour exécuter des commandes en toute sécurité
function safeExec(command, description) {
  console.log(`\n📦 ${description}...`);
  try {
    console.log(`Commande: ${command}`);
    execSync(command, { stdio: 'inherit', cwd: process.cwd() });
    console.log(`✅ ${description} - Succès`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} - Erreur:`, error.message);
    return false;
  }
}

// Étape 1: Vérifier package.json
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ package.json non trouvé dans:', process.cwd());
  console.log('💡 Assurez-vous d\'être dans le répertoire racine du projet');
  process.exit(1);
}

console.log('✅ package.json trouvé');

// Étape 2: Installation des dépendances de base si nécessaire
const nodeModulesPath = path.join(process.cwd(), 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.log('📦 Installation des dépendances de base...');
  if (!safeExec('npm install', 'Installation npm')) {
    console.log('⚠️ Tentative avec yarn...');
    safeExec('yarn install', 'Installation yarn');
  }
}

// Étape 3: Installation des outils de test essentiels (versions compatibles)
const testDeps = [
  'vitest@^2.0.0',
  '@vitest/ui@^2.0.0',
  '@testing-library/react@^16.0.0',
  '@testing-library/jest-dom@^6.0.0',
  '@playwright/test@^1.52.0',
  'jsdom@^25.0.0'
];

console.log('\n🧪 Installation des outils de test...');
const installCmd = `npm install --save-dev ${testDeps.join(' ')} --legacy-peer-deps`;
safeExec(installCmd, 'Installation des dépendances de test');

// Étape 4: Installation des navigateurs Playwright
console.log('\n🌐 Installation des navigateurs Playwright...');
safeExec('npx playwright install chromium', 'Installation Chromium');

// Étape 5: Création des dossiers de test
console.log('\n📁 Création de la structure de test...');
const testDirs = [
  'src/test',
  'src/test/mocks',
  'e2e'
];

testDirs.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`✅ Créé: ${dir}`);
  } else {
    console.log(`✅ Existe déjà: ${dir}`);
  }
});

// Étape 6: Création d'un fichier de configuration Vitest si nécessaire
const vitestConfigPath = path.join(process.cwd(), 'vitest.config.ts');
if (!fs.existsSync(vitestConfigPath)) {
  const vitestConfig = `import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
  },
})`;
  
  fs.writeFileSync(vitestConfigPath, vitestConfig);
  console.log('✅ Créé: vitest.config.ts');
}

// Étape 7: Création d'un fichier de setup pour les tests
const setupFilePath = path.join(process.cwd(), 'src/test/setup.ts');
if (!fs.existsSync(setupFilePath)) {
  const setupContent = `import '@testing-library/jest-dom'`;
  fs.writeFileSync(setupFilePath, setupContent);
  console.log('✅ Créé: src/test/setup.ts');
}

// Étape 8: Vérification finale
console.log('\n🔍 Vérification finale...');
try {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  console.log('✅ Projet:', packageJson.name || 'WeMa Tracker');
  console.log('✅ Version:', packageJson.version || '1.0.0');
} catch (error) {
  console.log('⚠️ Impossible de lire package.json');
}

// Affichage des commandes disponibles
console.log('\n🎯 Commandes de test disponibles:');
console.log('  npm test                - Tests de base');
console.log('  npx vitest             - Tests unitaires');
console.log('  npx playwright test    - Tests E2E');
console.log('  npx vitest --ui        - Interface graphique');

console.log('\n🎉 Configuration terminée !');
console.log('💡 Lancez: npm test ou npx vitest');
console.log('📖 Plus d\'infos: voir TEST_SETUP.md\n');
