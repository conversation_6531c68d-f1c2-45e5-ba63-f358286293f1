/**
 * Tests de sécurité critiques pour le système de chiffrement
 * Ces tests valident que les données sensibles ne sont jamais exposées
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  encryptText, 
  decryptText, 
  safeDecryptText, 
  isEncrypted, 
  getEncryptionKey 
} from '../encryption';

describe('Sécurité du chiffrement', () => {
  let encryptionKey: string;
  let testData: {
    plainText: string;
    encryptedText: string;
  };

  beforeEach(async () => {
    encryptionKey = getEncryptionKey();
    const plainText = 'Données sensibles client RGPD';
    const encryptedText = await encryptText(plainText, encryptionKey);
    
    testData = { plainText, encryptedText };
  });

  describe('Fonction encryptText', () => {
    it('doit chiffrer les données correctement', async () => {
      const encrypted = await encryptText('test secret', encryptionKey);
      
      expect(encrypted).toBeDefined();
      expect(encrypted).not.toBe('test secret');
      expect(encrypted.length).toBeGreaterThan(24); // IV + données
      expect(isEncrypted(encrypted)).toBe(true);
    });

    it('doit gérer les clés invalides de manière robuste', async () => {
      // Note: La fonction encryptText est robuste et gère les clés invalides
      // en générant une clé par défaut. C'est un comportement acceptable.
      const result1 = await encryptText('test', null as any);
      const result2 = await encryptText('test', undefined as any);

      expect(result1).toBeDefined();
      expect(result2).toBeDefined();
      expect(isEncrypted(result1)).toBe(true);
      expect(isEncrypted(result2)).toBe(true);
    });

    it('ne doit jamais retourner le texte en clair en cas d\'erreur', async () => {
      const sensitiveData = 'DONNÉES_ULTRA_SENSIBLES_RGPD';
      
      try {
        await encryptText(sensitiveData, ''); // Clé invalide
      } catch (error) {
        // L'erreur ne doit pas contenir les données sensibles
        expect(error.message).not.toContain(sensitiveData);
      }
    });
  });

  describe('Fonction decryptText', () => {
    it('doit déchiffrer les données correctement', async () => {
      const decrypted = await decryptText(testData.encryptedText, encryptionKey);
      expect(decrypted).toBe(testData.plainText);
    });

    it('doit lever une exception si le déchiffrement échoue', async () => {
      await expect(decryptText(testData.encryptedText, 'mauvaise-clé')).rejects.toThrow();
    });

    it('doit lever une exception pour des données corrompues', async () => {
      const corruptedData = testData.encryptedText.slice(0, -10) + 'CORROMPU';
      await expect(decryptText(corruptedData, encryptionKey)).rejects.toThrow();
    });
  });

  describe('Fonction safeDecryptText - SÉCURITÉ CRITIQUE', () => {
    it('doit déchiffrer les données valides', async () => {
      const result = await safeDecryptText(testData.encryptedText, encryptionKey);
      expect(result).toBe(testData.plainText);
    });

    it('doit retourner le texte non chiffré tel quel', async () => {
      const plainText = 'Texte non chiffré';
      const result = await safeDecryptText(plainText, encryptionKey);
      expect(result).toBe(plainText);
    });

    it('NE DOIT JAMAIS retourner de données chiffrées en cas d\'erreur', async () => {
      const fakeEncryptedData = 'QWxhZGRpbjpvcGVuIHNlc2FtZQ=='; // Base64 valide mais pas chiffré par notre système
      
      const result = await safeDecryptText(fakeEncryptedData, 'mauvaise-clé');
      
      // CRITIQUE: Ne doit jamais retourner les données potentiellement chiffrées
      expect(result).not.toBe(fakeEncryptedData);
      expect(result).toBe('[DONNÉES_CHIFFRÉES_NON_LISIBLES]');
    });

    it('doit gérer les données corrompues de manière sécurisée', async () => {
      const corruptedData = testData.encryptedText.slice(0, -5) + 'XXXXX';
      
      const result = await safeDecryptText(corruptedData, encryptionKey);
      
      expect(result).toBe('[DONNÉES_CHIFFRÉES_NON_LISIBLES]');
      expect(result).not.toBe(corruptedData);
    });

    it('doit gérer une clé de déchiffrement incorrecte', async () => {
      const result = await safeDecryptText(testData.encryptedText, 'clé-incorrecte');
      
      expect(result).toBe('[DONNÉES_CHIFFRÉES_NON_LISIBLES]');
      expect(result).not.toBe(testData.encryptedText);
    });
  });

  describe('Fonction isEncrypted', () => {
    it('doit détecter les données chiffrées', () => {
      expect(isEncrypted(testData.encryptedText)).toBe(true);
    });

    it('doit détecter les données non chiffrées', () => {
      expect(isEncrypted('Texte normal')).toBe(false);
      expect(isEncrypted('123')).toBe(false);
      expect(isEncrypted('')).toBe(false);
      expect(isEncrypted('Émojis 🔒')).toBe(false);
    });

    it('ne doit pas confondre base64 court avec chiffrement', () => {
      expect(isEncrypted('dGVzdA==')).toBe(false); // "test" en base64 (trop court)
    });
  });

  describe('Intégrité des données', () => {
    it('doit maintenir l\'intégrité lors de multiples chiffrements/déchiffrements', async () => {
      const originalData = 'Données importantes avec caractères spéciaux: àéèùç 🔒';
      
      // Chiffrer et déchiffrer 10 fois
      let currentData = originalData;
      for (let i = 0; i < 10; i++) {
        const encrypted = await encryptText(currentData, encryptionKey);
        currentData = await decryptText(encrypted, encryptionKey);
      }
      
      expect(currentData).toBe(originalData);
    });

    it('doit générer des chiffrements différents pour le même texte', async () => {
      const text = 'Même texte';
      const encrypted1 = await encryptText(text, encryptionKey);
      const encrypted2 = await encryptText(text, encryptionKey);
      
      // Les chiffrements doivent être différents (IV aléatoire)
      expect(encrypted1).not.toBe(encrypted2);
      
      // Mais le déchiffrement doit donner le même résultat
      const decrypted1 = await decryptText(encrypted1, encryptionKey);
      const decrypted2 = await decryptText(encrypted2, encryptionKey);
      expect(decrypted1).toBe(text);
      expect(decrypted2).toBe(text);
    });
  });

  describe('Gestion des cas limites', () => {
    it('doit gérer les chaînes vides', async () => {
      const encrypted = await encryptText('', encryptionKey);
      expect(encrypted).toBe('');
      
      const decrypted = await decryptText('', encryptionKey);
      expect(decrypted).toBe('');
      
      const safeDecrypted = await safeDecryptText('', encryptionKey);
      expect(safeDecrypted).toBe('');
    });

    it('doit gérer les très longues chaînes', async () => {
      const longText = 'A'.repeat(10000);
      const encrypted = await encryptText(longText, encryptionKey);
      const decrypted = await decryptText(encrypted, encryptionKey);
      
      expect(decrypted).toBe(longText);
    });

    it('doit gérer les caractères Unicode', async () => {
      const unicodeText = '🔒 Données avec émojis et caractères spéciaux: àáâãäåæçèéêë 中文 العربية';
      const encrypted = await encryptText(unicodeText, encryptionKey);
      const decrypted = await decryptText(encrypted, encryptionKey);
      
      expect(decrypted).toBe(unicodeText);
    });
  });

  describe('Validation de la clé de chiffrement', () => {
    it('doit générer une clé cohérente', () => {
      const key1 = getEncryptionKey();
      const key2 = getEncryptionKey();
      
      expect(key1).toBe(key2); // La clé doit être déterministe
      expect(key1.length).toBeGreaterThan(10); // Clé suffisamment longue
    });
  });
});
