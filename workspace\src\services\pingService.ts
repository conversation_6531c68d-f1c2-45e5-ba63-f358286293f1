/**
 * Service pour la gestion des pings
 * Contient les fonctions liées aux opérations sur les pings
 */

import { supabase } from "@/integrations/supabase/client";
import { Ping } from "@/types";
import { encryptText, safeDecryptText, getEncryptionKey } from "@/utils/encryption";
import { sendDesktopNotification } from "@/utils/notifications";

/**
 * Récupère les pings reçus par un utilisateur
 * @param userId ID de l'utilisateur
 * @returns Liste des pings reçus
 */
export async function getReceivedPings(userId: string): Promise<Ping[]> {
  try {
    const { data, error } = await supabase
      .from('pings')
      .select('*, from_user:assignees!from_user_id(name)')
      .eq('to_user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    
    // Déchiffrer les messages de manière sécurisée
    const encryptionKey = getEncryptionKey();
    const pings = await Promise.all(data.map(async ping => {
      const message = await safeDecryptText(ping.message, encryptionKey);

      // Les noms d'utilisateurs ne sont pas chiffrés selon la configuration
      const fromUserName = ping.from_user.name;
      
      return {
        id: ping.id,
        fromUserId: ping.from_user_id,
        fromUserName,
        toUserId: ping.to_user_id,
        message,
        read: ping.read,
        createdAt: ping.created_at
      };
    }));
    
    return pings;
  } catch (error) {
    console.error('Erreur lors de la récupération des pings reçus:', error);
    return [];
  }
}

/**
 * Récupère les pings envoyés par un utilisateur
 * @param userId ID de l'utilisateur
 * @returns Liste des pings envoyés
 */
export async function getSentPings(userId: string): Promise<Ping[]> {
  try {
    const { data, error } = await supabase
      .from('pings')
      .select('*, to_user:assignees!to_user_id(name)')
      .eq('from_user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    
    // Déchiffrer les messages de manière sécurisée
    const encryptionKey = getEncryptionKey();
    const pings = await Promise.all(data.map(async ping => {
      const message = await safeDecryptText(ping.message, encryptionKey);

      // Les noms d'utilisateurs ne sont pas chiffrés selon la configuration
      const toUserName = ping.to_user.name;
      
      return {
        id: ping.id,
        fromUserId: ping.from_user_id,
        toUserId: ping.to_user_id,
        toUserName,
        message,
        read: ping.read,
        createdAt: ping.created_at
      };
    }));
    
    return pings;
  } catch (error) {
    console.error('Erreur lors de la récupération des pings envoyés:', error);
    return [];
  }
}

/**
 * Envoie un ping à un utilisateur
 * @param fromUserId ID de l'expéditeur
 * @param toUserId ID du destinataire
 * @param message Message du ping
 * @param sendDesktopNotif Envoyer une notification de bureau
 * @returns Le ping créé ou null en cas d'erreur
 */
export async function sendPing(
  fromUserId: string,
  toUserId: string,
  message: string,
  sendDesktopNotif: boolean = true
): Promise<Ping | null> {
  try {
    // Chiffrer le message
    const encryptionKey = getEncryptionKey();
    const encryptedMessage = await encryptText(message, encryptionKey);
    
    // Insérer le ping
    const { data, error } = await supabase
      .from('pings')
      .insert({
        from_user_id: fromUserId,
        to_user_id: toUserId,
        message: encryptedMessage,
        read: false
      })
      .select()
      .single();
    
    if (error) throw error;
    
    // Envoyer une notification de bureau si demandé
    if (sendDesktopNotif) {
      // Récupérer le nom de l'expéditeur
      const { data: fromUserData } = await supabase
        .from('assignees')
        .select('name')
        .eq('id', fromUserId)
        .single();
      
      // Les noms d'utilisateurs ne sont pas chiffrés selon la configuration
      const fromUserName = fromUserData?.name || 'Utilisateur inconnu';
      
      sendDesktopNotification(
        `Nouveau message de ${fromUserName}`,
        message,
        "/logo.png"
      );
    }
    
    return {
      id: data.id,
      fromUserId,
      toUserId,
      message, // Retourner le message en clair
      read: false,
      createdAt: data.created_at
    };
  } catch (error) {
    console.error('Erreur lors de l\'envoi du ping:', error);
    return null;
  }
}

/**
 * Marque un ping comme lu
 * @param pingId ID du ping
 * @returns true si la mise à jour a réussi, false sinon
 */
export async function markPingAsRead(pingId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('pings')
      .update({ read: true })
      .eq('id', pingId);
    
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error('Erreur lors du marquage du ping comme lu:', error);
    return false;
  }
}
