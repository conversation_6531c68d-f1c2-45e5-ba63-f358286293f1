import { describe, it, expect, beforeEach } from 'vitest'
import { clientService } from '../clientService'

describe('ClientService Integration Tests', () => {
  beforeEach(() => {
    // Setup avant chaque test
  })

  it('should fetch clients from API', async () => {
    const clients = await clientService.getClients()
    
    expect(Array.isArray(clients)).toBe(true)
    expect(clients).toHaveLength(1)
    expect(clients[0]).toHaveProperty('id')
    expect(clients[0]).toHaveProperty('name')
    expect(clients[0]).toHaveProperty('email')
  })

  it('should create a new client', async () => {
    const newClient = {
      name: 'Test Client',
      email: '<EMAIL>'
    }

    const createdClient = await clientService.createClient(newClient)
    
    expect(createdClient).toHaveProperty('id')
    expect(createdClient.name).toBe(newClient.name)
    expect(createdClient.email).toBe(newClient.email)
  })

  it('should handle API errors gracefully', async () => {
    // Test de gestion d'erreur
    await expect(clientService.getClient('invalid-id')).rejects.toThrow()
  })
}) 