import { test, expect } from '@playwright/test'

test.describe('WeMa Tracker E2E Tests', () => {
  test('should load the homepage', async ({ page }) => {
    await page.goto('/')
    
    // Vérifier que la page se charge
    await expect(page).toHaveTitle(/WeMa Tracker/i)
    
    // Vérifier la présence du logo
    const logo = page.locator('img[alt*="WeMa"]')
    await expect(logo).toBeVisible()
  })

  test('should navigate to profile creation', async ({ page }) => {
    await page.goto('/')
    
    // Cliquer sur le bouton de création de profil
    const createProfileButton = page.getByText('Créer un profil')
    if (await createProfileButton.isVisible()) {
      await createProfileButton.click()
      
      // Vérifier la navigation
      await expect(page).toHaveURL(/.*profile.*/)
    }
  })

  test('should display responsive design', async ({ page }) => {
    await page.goto('/')
    
    // Test sur différentes tailles d'écran
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('body')).toBeVisible()
    
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('body')).toBeVisible()
  })
}) 